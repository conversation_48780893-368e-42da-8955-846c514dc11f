{"name": "@automarket/workflows", "version": "0.1.0", "description": "Workflow engine for AutoMarket", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@automarket/core": "workspace:*", "@automarket/providers": "workspace:*", "@automarket/assets": "workspace:*", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@types/uuid": "^10.0.0", "typescript": "^5", "jest": "^29.7.0", "eslint": "^8", "rimraf": "^6.0.1"}, "files": ["dist"], "publishConfig": {"access": "restricted"}}