import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Heading,
  Text,
  Stack,
  SimpleGrid,
  Badge,
  Flex,
  Dialog,
  Spinner,
  Field,
  Input,
} from '@chakra-ui/react';
import { CompositionProject } from '../models/CompositionProject';

// Date formatting helper
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

// Template card component
interface TemplateCardProps {
  template: CompositionProject;
  onSelect: (template: CompositionProject) => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ template, onSelect }) => {
  return (
    <Box
      borderWidth="1px"
      borderRadius="lg"
      overflow="hidden"
      p={4}
      bg="white"
      boxShadow="sm"
      transition="all 0.2s"
      _hover={{ boxShadow: 'md', cursor: 'pointer' }}
      onClick={() => onSelect(template)}
    >
      <Flex justify="space-between" align="center" mb={2}>
        <Heading size="md">{template.name}</Heading>
        <Badge colorScheme="purple">Template</Badge>
      </Flex>
      
      {template.description && (
        <Text color="gray.600" mb={2}>{template.description}</Text>
      )}
      
      <Flex direction="column" mt={3} fontSize="sm" color="gray.500">
        <Text>Created: {formatDate(template.dateCreated)}</Text>
        <Text>Modified: {formatDate(template.dateModified)}</Text>
        <Text>Version: {template.version}</Text>
      </Flex>
      
      <Button mt={4} size="sm" colorScheme="blue" width="full">
        Use Template
      </Button>
    </Box>
  );
};

// New project from template form
interface CreateFromTemplateFormProps {
  template: CompositionProject;
  onSubmit: (name: string, description: string) => Promise<void>;
  onCancel: () => void;
  isLoading: boolean;
}

const CreateFromTemplateForm: React.FC<CreateFromTemplateFormProps> = ({
  template,
  onSubmit,
  onCancel,
  isLoading,
}) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  
  useEffect(() => {
    // Pre-fill with template name/description
    setName(`${template.name} (Copy)`);
    setDescription(template.description || '');
  }, [template]);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(name, description);
  };
  
  return (
    <Box as="form" onSubmit={handleSubmit}>
      <Stack gap={4} align="stretch">
        <Field.Root required>
          <Field.Label>Project Name</Field.Label>
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter project name"
          />
        </Field.Root>
        
        <Field.Root>
          <Field.Label>Description</Field.Label>
          <Input
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter project description (optional)"
          />
        </Field.Root>
        
        <Stack direction="row" justify="flex-end" gap={3}>
          <Button onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            type="submit"
            colorScheme="blue"
            loading={isLoading}
            loadingText="Creating"
          >
            Create Project
          </Button>
        </Stack>
      </Stack>
    </Box>
  );
};

// Main template selection component
interface TemplateSelectionProps {
  onProjectCreated: (projectId: string) => void;
  open: boolean;
  onClose: () => void;
}

const TemplateSelection: React.FC<TemplateSelectionProps> = ({
  onProjectCreated,
  open,
  onClose,
}) => {
  const [templates, setTemplates] = useState<CompositionProject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState<CompositionProject | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  
  // Fetch templates on component mount
  useEffect(() => {
    if (open) {
      fetchTemplates();
    }
  }, [open]);
  
  // Fetch templates from API
  const fetchTemplates = async () => {
    try {
      setIsLoading(true);
      
      const res = await fetch('/api/composition/project?templates=true');
      const data = await res.json();
      
      if (!res.ok) {
        throw new Error(data.message || 'Failed to fetch templates');
      }
      
      setTemplates(data.projects || []);
    } catch (error) {
      console.error('Error fetching templates:', error);
      // Toast notification removed
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle template selection
  const handleSelectTemplate = (template: CompositionProject) => {
    setSelectedTemplate(template);
  };
  
  // Handle form submit
  const handleCreateFromTemplate = async (name: string, description: string) => {
    if (!selectedTemplate) return;
    
    try {
      setIsCreating(true);
      
      const res = await fetch(`/api/composition/project/${selectedTemplate.id}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: name,
          description,
        }),
      });
      
      if (!res.ok) {
        const data = await res.json();
        throw new Error(data.message || 'Failed to create project from template');
      }
      
      const { project: newProject } = await res.json();
      
      // Toast notification removed
      
      // Callback when project is created
      onProjectCreated(newProject.id);
      onClose();
    } catch (error) {
      console.error('Error creating project from template:', error);
      // Toast notification removed
    } finally {
      setIsCreating(false);
    }
  };
  
  return (
    <Dialog open={open} onClose={onClose} size="xl">
      <Dialog.Backdrop />
      <Dialog.Content>
        <Dialog.Header>Create from Template</Dialog.Header>
        <Dialog.CloseButton />
        <Dialog.Body>
          {selectedTemplate ? (
            <CreateFromTemplateForm
              template={selectedTemplate}
              onSubmit={handleCreateFromTemplate}
              onCancel={() => setSelectedTemplate(null)}
              isLoading={isCreating}
            />
          ) : (
            <>
              <Text mb={4}>
                Select a template to create a new composition project:
              </Text>
              
              {isLoading ? (
                <Flex justify="center" py={10}>
                  <Spinner />
                </Flex>
              ) : templates.length === 0 ? (
                <Box textAlign="center" py={6}>
                  <Text color="gray.500">
                    No template projects found
                  </Text>
                </Box>
              ) : (
                <SimpleGrid columns={{ base: 1, md: 2 }} gap={4}>
                  {templates.map((template) => (
                    <TemplateCard
                      key={template.id}
                      template={template}
                      onSelect={handleSelectTemplate}
                    />
                  ))}
                </SimpleGrid>
              )}
            </>
          )}
        </Dialog.Body>
        <Dialog.Footer>
          <Button onClick={onClose}>Close</Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};

export default TemplateSelection;
