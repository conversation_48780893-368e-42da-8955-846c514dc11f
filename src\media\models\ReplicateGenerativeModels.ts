/**
 * Replicate Model Implementations for Generative AI
 * 
 * Specific model implementations for TextToImage, TextToVideo, and ImageToVideo
 * using Replicate models like FLUX, Luma Dream Machine, and Stable Video Diffusion
 */

import { ModelMetadata } from './Model';
import { TextToImageModel, TextToImageOptions, Image } from './TextToImageModel';
import { TextToVideoModel, TextToVideoOptions } from './TextToVideoModel';
import { ImageToVideoModel, ImageToVideoOptions, ImageInput, castToImage } from './ImageToVideoModel';
import { Video } from '../assets/roles';
import { ReplicateClient, ReplicateModelMetadata } from '../clients/ReplicateClient';
import { TextInput, castToText } from '../assets/casting';
import Replicate from 'replicate';

export interface ReplicateModelConfig {
  client: ReplicateClient;
  modelMetadata: ReplicateModelMetadata;
  replicateClient: Replicate;
}

/**
 * ReplicateTextToImageModel - Implements TextToImageModel for Replicate image models
 * 
 * Handles models like FLUX, SDXL, etc.
 */
export class ReplicateTextToImageModel extends TextToImageModel {
  private client: ReplicateClient;
  private modelMetadata: ReplicateModelMetadata;
  private replicateClient: Replicate;

  constructor(config: ReplicateModelConfig) {
    const metadata: ModelMetadata = {
      id: config.modelMetadata.id,
      name: config.modelMetadata.name || config.modelMetadata.id,
      description: config.modelMetadata.description || '',
      version: '1.0.0',
      provider: 'replicate',
      capabilities: ['text-to-image'],
      inputTypes: ['text'],
      outputTypes: ['image']
    };

    super(metadata);

    this.client = config.client;
    this.modelMetadata = config.modelMetadata;
    this.replicateClient = config.replicateClient;
  }

  async transform(input: TextInput, options?: TextToImageOptions): Promise<Image> {
    const text = await castToText(input);

    if (!text.isValid()) {
      throw new Error('Invalid text data provided');
    }

    try {
      const replicateInput = this.prepareImageInput(text.content, options);

      const prediction = await this.replicateClient.predictions.create({
        version: this.modelMetadata.id,
        input: replicateInput
      });

      // Poll for completion
      let finalPrediction = prediction;
      while (finalPrediction.status === 'starting' || finalPrediction.status === 'processing') {
        await new Promise(resolve => setTimeout(resolve, 1000));
        finalPrediction = await this.replicateClient.predictions.get(prediction.id);
      }

      if (finalPrediction.status === 'succeeded') {
        // Create Image from result URL
        const imageUrl = Array.isArray(finalPrediction.output) 
          ? finalPrediction.output[0] 
          : finalPrediction.output;

        return Image.fromUrl(imageUrl, {
          originalPrompt: text.content,
          modelUsed: this.modelMetadata.id,
          options: options
        });
      } else {
        throw new Error(String(finalPrediction.error) || 'Image generation failed');
      }
    } catch (error) {
      throw new Error(`Replicate text-to-image failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private prepareImageInput(prompt: string, options?: TextToImageOptions): any {
    const input: any = { prompt };

    // Map common options to model parameters
    const params = this.modelMetadata.parameters || {};

    if (options?.width && params.width) input.width = options.width;
    if (options?.height && params.height) input.height = options.height;
    if (options?.aspectRatio && params.aspect_ratio) input.aspect_ratio = options.aspectRatio;
    if (options?.seed && params.seed) input.seed = options.seed;
    if (options?.negativePrompt && params.negative_prompt) input.negative_prompt = options.negativePrompt;
    if (options?.guidanceScale && params.guidance_scale) input.guidance_scale = options.guidanceScale;
    if (options?.steps && params.num_inference_steps) input.num_inference_steps = options.steps;

    // Add model defaults
    Object.keys(params).forEach(paramName => {
      const param = params[paramName];
      if (param.default !== undefined && !(paramName in input)) {
        input[paramName] = param.default;
      }
    });

    return input;
  }

  async isAvailable(): Promise<boolean> {
    return await this.client.testConnection();
  }
}

/**
 * ReplicateTextToVideoModel - Implements TextToVideoModel for Replicate video models
 * 
 * Handles models like Luma Dream Machine, Runway Gen-3, etc.
 */
export class ReplicateTextToVideoModel extends TextToVideoModel {
  private client: ReplicateClient;
  private modelMetadata: ReplicateModelMetadata;
  private replicateClient: Replicate;

  constructor(config: ReplicateModelConfig) {
    const metadata: ModelMetadata = {
      id: config.modelMetadata.id,
      name: config.modelMetadata.name || config.modelMetadata.id,
      description: config.modelMetadata.description || '',
      version: '1.0.0',
      provider: 'replicate',
      capabilities: ['text-to-video'],
      inputTypes: ['text'],
      outputTypes: ['video']
    };

    super(metadata);

    this.client = config.client;
    this.modelMetadata = config.modelMetadata;
    this.replicateClient = config.replicateClient;
  }

  async transform(input: TextInput, options?: TextToVideoOptions): Promise<Video> {
    const text = await castToText(input);

    if (!text.isValid()) {
      throw new Error('Invalid text data provided');
    }

    try {
      const replicateInput = this.prepareVideoInput(text.content, options);

      const prediction = await this.replicateClient.predictions.create({
        version: this.modelMetadata.id,
        input: replicateInput
      });

      // Poll for completion (video generation takes longer)
      let finalPrediction = prediction;
      while (finalPrediction.status === 'starting' || finalPrediction.status === 'processing') {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Check every 5 seconds
        finalPrediction = await this.replicateClient.predictions.get(prediction.id);
      }

      if (finalPrediction.status === 'succeeded') {
        // Create Video from result URL
        const videoUrl = Array.isArray(finalPrediction.output) 
          ? finalPrediction.output[0] 
          : finalPrediction.output;

        return Video.fromUrl(videoUrl, {
          originalPrompt: text.content,
          modelUsed: this.modelMetadata.id,
          options: options
        });
      } else {
        throw new Error(String(finalPrediction.error) || 'Video generation failed');
      }
    } catch (error) {
      throw new Error(`Replicate text-to-video failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private prepareVideoInput(prompt: string, options?: TextToVideoOptions): any {
    const input: any = { prompt };

    // Map common options to model parameters
    const params = this.modelMetadata.parameters || {};

    if (options?.duration && params.duration) input.duration = options.duration;
    if (options?.aspectRatio && params.aspect_ratio) input.aspect_ratio = options.aspectRatio;
    if (options?.fps && params.fps) input.fps = options.fps;
    if (options?.loop && params.loop) input.loop = options.loop;
    if (options?.seed && params.seed) input.seed = options.seed;

    // Add model defaults
    Object.keys(params).forEach(paramName => {
      const param = params[paramName];
      if (param.default !== undefined && !(paramName in input)) {
        input[paramName] = param.default;
      }
    });

    return input;
  }

  async isAvailable(): Promise<boolean> {
    return await this.client.testConnection();
  }
}

/**
 * ReplicateImageToVideoModel - Implements ImageToVideoModel for Replicate models
 * 
 * Handles models like Stable Video Diffusion, etc.
 */
export class ReplicateImageToVideoModel extends ImageToVideoModel {
  private client: ReplicateClient;
  private modelMetadata: ReplicateModelMetadata;
  private replicateClient: Replicate;

  constructor(config: ReplicateModelConfig) {
    const metadata: ModelMetadata = {
      id: config.modelMetadata.id,
      name: config.modelMetadata.name || config.modelMetadata.id,
      description: config.modelMetadata.description || '',
      version: '1.0.0',
      provider: 'replicate',
      capabilities: ['image-to-video'],
      inputTypes: ['image'],
      outputTypes: ['video']
    };

    super(metadata);

    this.client = config.client;
    this.modelMetadata = config.modelMetadata;
    this.replicateClient = config.replicateClient;
  }

  async transform(input: ImageInput, options?: ImageToVideoOptions): Promise<Video> {
    const image = await castToImage(input);

    if (!image.isValid()) {
      throw new Error('Invalid image data provided');
    }

    try {
      const replicateInput = await this.prepareImageToVideoInput(image, options);

      const prediction = await this.replicateClient.predictions.create({
        version: this.modelMetadata.id,
        input: replicateInput
      });

      // Poll for completion
      let finalPrediction = prediction;
      while (finalPrediction.status === 'starting' || finalPrediction.status === 'processing') {
        await new Promise(resolve => setTimeout(resolve, 5000));
        finalPrediction = await this.replicateClient.predictions.get(prediction.id);
      }

      if (finalPrediction.status === 'succeeded') {
        // Create Video from result URL
        const videoUrl = Array.isArray(finalPrediction.output) 
          ? finalPrediction.output[0] 
          : finalPrediction.output;

        return Video.fromUrl(videoUrl, {
          sourceImage: image.metadata.url || 'buffer',
          modelUsed: this.modelMetadata.id,
          options: options
        });
      } else {
        throw new Error(String(finalPrediction.error) || 'Image-to-video generation failed');
      }
    } catch (error) {
      throw new Error(`Replicate image-to-video failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async prepareImageToVideoInput(image: Image, options?: ImageToVideoOptions): Promise<any> {
    const input: any = {};

    // Handle image input - need to upload or provide URL
    if (image.metadata.url) {
      input.image = image.metadata.url;
    } else {
      // TODO: Upload image buffer to Replicate
      throw new Error('Image upload not yet implemented - please provide image URL');
    }

    // Map common options to model parameters
    const params = this.modelMetadata.parameters || {};

    if (options?.duration && params.duration) input.duration = options.duration;
    if (options?.fps && params.fps) input.fps = options.fps;
    if (options?.motionStrength && params.motion_bucket_id) {
      input.motion_bucket_id = Math.round(options.motionStrength * 255);
    }
    if (options?.seed && params.seed) input.seed = options.seed;
    if (options?.prompt && params.prompt) input.prompt = options.prompt;

    // Add model defaults
    Object.keys(params).forEach(paramName => {
      const param = params[paramName];
      if (param.default !== undefined && !(paramName in input)) {
        input[paramName] = param.default;
      }
    });

    return input;
  }

  async isAvailable(): Promise<boolean> {
    return await this.client.testConnection();
  }
}

export { ReplicateModelConfig };
