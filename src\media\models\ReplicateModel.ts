/**
 * ReplicateModel - Generic Implementation for Replicate Models
 * 
 * Generic model that can implement any Replicate model type by taking
 * metadata in the constructor and using ReplicateClient for API calls.
 */

import { Model, ModelMetadata } from './Model';
import { TextToSpeechModel, TextToSpeechOptions } from './TextToSpeechModel';
import { ReplicateClient, ReplicateModelMetadata } from '../clients/ReplicateClient';
import { Text, Speech, Audio } from '../assets/roles';
import { TextInput, castToText } from '../assets/casting';
import Replicate from 'replicate';

export interface ReplicateModelConfig {
  client: ReplicateClient;
  modelMetadata: ReplicateModelMetadata;
  replicateClient: Replicate;
}

/**
 * Generic Replicate model that can adapt to any Replicate model type
 */
export class ReplicateModel<TInput = any, TOptions = any, TOutput = any> extends Model<TInput, TOptions, TOutput> {
  protected client: ReplicateClient;
  protected modelMetadata: ReplicateModelMetadata;
  protected replicateClient: Replicate;
  constructor(config: ReplicateModelConfig) {
    // Convert ReplicateModelMetadata to ModelMetadata
    const outputTypes = ReplicateModel.getOutputTypesFromCategory(config.modelMetadata.category);
    
    const metadata: ModelMetadata = {
      id: config.modelMetadata.id,
      name: config.modelMetadata.name || config.modelMetadata.id,
      description: config.modelMetadata.description || '',
      version: '1.0.0', // Replicate doesn't have version in metadata
      provider: 'replicate',
      capabilities: [config.modelMetadata.category], // Use category as capability
      inputTypes: ['text'], // Default - can be overridden
      outputTypes: outputTypes
    };

    super(metadata);

    this.client = config.client;
    this.modelMetadata = config.modelMetadata;
    this.replicateClient = config.replicateClient;
  }

  private static getOutputTypesFromCategory(category: string): string[] {
    if (category.includes('image')) return ['image'];
    if (category.includes('video')) return ['video'];
    if (category.includes('audio') || category.includes('speech')) return ['audio'];
    return ['any'];
  }

  /**
   * Core transform method - delegates to Replicate API
   */
  async transform(input: TInput, options?: TOptions): Promise<TOutput> {
    const startTime = Date.now();

    try {
      // Create prediction using Replicate API
      const prediction = await this.replicateClient.predictions.create({
        version: this.modelMetadata.id, // Use the full model ID
        input: this.prepareInput(input),
      });

      // Wait for completion
      const finalPrediction = await this.replicateClient.predictions.get(prediction.id);

      if (finalPrediction.status === 'succeeded') {
        return {
          success: true,
          data: this.processOutput(finalPrediction.output) as TOutput,
          metadata: {
            processingTime: Date.now() - startTime,
            modelUsed: this.modelMetadata.id,
            provider: 'replicate',
            predictionId: prediction.id
          }
        };
      } else if (finalPrediction.status === 'failed') {
        return {
          success: false,
          error: String(finalPrediction.error) || 'Prediction failed',
          metadata: {
            processingTime: Date.now() - startTime,
            modelUsed: this.modelMetadata.id,
            provider: 'replicate',
            predictionId: prediction.id
          }
        };
      } else {
        return {
          success: false,
          error: `Prediction in unexpected state: ${finalPrediction.status}`,
          metadata: {
            processingTime: Date.now() - startTime,
            modelUsed: this.modelMetadata.id,
            provider: 'replicate',
            predictionId: prediction.id
          }
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          processingTime: Date.now() - startTime,
          modelUsed: this.modelMetadata.id,
          provider: 'replicate'
        }
      };
    }
  }

  /**
   * Prepare input for Replicate API based on model type
   */
  protected prepareInput(input: TInput): any {
    // Basic input preparation - can be overridden for specific model types
    if (typeof input === 'string') {
      // Text input - common for text-to-image, text-to-video
      return { prompt: input };
    } else if (typeof input === 'object' && input !== null) {
      // Object input - pass through
      return input;
    } else {
      // Simple value - wrap in prompt
      return { prompt: String(input) };
    }
  }

  /**
   * Process output from Replicate API
   */
  protected processOutput(output: any): any {
    // Basic output processing - can be overridden for specific model types
    if (Array.isArray(output)) {
      return output[0]; // Return first result for now
    }
    return output;
  }

  /**
   * Get input schema for this model
   */
  getInputSchema(): any {
    return this.modelMetadata.parameters || {};
  }
  /**
   * Get output schema for this model
   */
  getOutputSchema(): any {
    // Basic schema based on model category
    return {
      type: ReplicateModel.getOutputTypesFromCategory(this.modelMetadata.category)[0] || 'any',
      description: `Output from ${this.modelMetadata.name}`
    };
  }

  /**
   * Check if model is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      return await this.client.testConnection();
    } catch {
      return false;
    }
  }
}

/**
 * Specialized ReplicateTextToSpeechModel that extends TextToSpeechModel
 */
export class ReplicateTextToSpeechModel extends TextToSpeechModel {
  private replicateModel: ReplicateModel<any, string>;

  constructor(config: ReplicateModelConfig) {
    // Create metadata for TextToSpeechModel
    const metadata: ModelMetadata = {
      id: config.modelMetadata.id,
      name: config.modelMetadata.name || config.modelMetadata.id,
      description: config.modelMetadata.description || '',
      version: '1.0.0',
      provider: 'replicate',
      capabilities: ['text-to-speech'],
      inputTypes: ['text'],
      outputTypes: ['audio']
    };

    super(metadata);

    // Create internal ReplicateModel for API calls
    this.replicateModel = new ReplicateModel<any, string>(config);
  }

  /**
   * Transform text to speech using Replicate TTS model
   */
  async transform(input: TextInput, options?: TextToSpeechOptions): Promise<Speech>;
  async transform(text: TextInput, voiceAudio: Speech, options?: TextToSpeechOptions): Promise<Speech>;
  async transform(input: TextInput, voiceAudioOrOptions?: Speech | TextToSpeechOptions, options?: TextToSpeechOptions): Promise<Speech> {
    // Cast input to Text
    const text = await castToText(input);

    if (!text.isValid()) {
      throw new Error('Invalid text data provided');
    }

    // Determine if second parameter is voice audio or options
    let voiceAudio: Speech | undefined;
    let actualOptions: TextToSpeechOptions | undefined;

    if (voiceAudioOrOptions && typeof voiceAudioOrOptions === 'object' && 'data' in voiceAudioOrOptions) {
      // Second parameter is Speech (voice audio)
      voiceAudio = voiceAudioOrOptions as Speech;
      actualOptions = options;
    } else {
      // Second parameter is options
      actualOptions = voiceAudioOrOptions as TextToSpeechOptions;
    }

    try {
      // Prepare input for Replicate TTS model
      const replicateInput: any = {
        text: text.content
      };

      // Add voice cloning if provided
      if (voiceAudio) {
        // For voice cloning, we'd need to upload the audio file first
        // For now, just note that voice cloning was requested
        console.log('Voice cloning requested but not yet implemented for Replicate models');
      }

      // Add options
      if (actualOptions?.language) {
        replicateInput.language = actualOptions.language;
      }
      if (actualOptions?.speed) {
        replicateInput.speed = actualOptions.speed;
      }

      // Call internal ReplicateModel
      const result = await this.replicateModel.transform(replicateInput);

      if (result.success && result.data) {
        // For now, we'll need to download the audio from the URL to create a Speech object
        // This is a simplified implementation - in practice you'd want to stream/download the audio
        console.log('TTS result URL:', result.data);
        
        // Create empty buffer for now - TODO: Download audio from URL
        const audioBuffer = Buffer.alloc(0);
        const speech = new Speech(audioBuffer, {
          originalText: text.content,
          resultUrl: result.data, // Store the URL in metadata
          options: actualOptions,
          ...result.metadata
        });

        return speech;
      } else {
        throw new Error(result.error || 'TTS transformation failed');
      }
    } catch (error) {
      throw new Error(`Replicate TTS failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if model is available
   */
  async isAvailable(): Promise<boolean> {
    return await this.replicateModel.isAvailable();
  }
}

export default ReplicateModel;
