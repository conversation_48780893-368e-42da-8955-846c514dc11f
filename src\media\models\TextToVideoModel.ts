/**
 * TextToVideoModel - Abstract Base Class
 * 
 * Abstract base class for text-to-video generation models.
 * Uses Asset-role system with automatic casting.
 */

import { Model, ModelMetadata } from './Model';
import { Text, Video } from '../assets/roles';
import { TextInput, castToText } from '../assets/casting';

export interface TextToVideoOptions {
  duration?: number; // Video duration in seconds
  width?: number;
  height?: number;
  aspectRatio?: string;
  fps?: number; // Frames per second
  quality?: number;
  format?: 'mp4' | 'webm' | 'mov';
  seed?: number;
  motionStrength?: number;
  loop?: boolean;
  negativePrompt?: string;
  guidanceScale?: number;
  steps?: number;
  [key: string]: any; // Allow model-specific parameters
}

/**
 * Abstract base class for text-to-video models
 */
export abstract class TextToVideoModel extends Model<TextInput, TextToVideoOptions, Video> {
  protected metadata: ModelMetadata;

  constructor(metadata: ModelMetadata) {
    super(metadata);
    this.metadata = metadata;
  }
  
  /**
   * Transform text to video
   */
  abstract transform(input: TextInput, options?: TextToVideoOptions): Promise<Video>;

  /**
   * Check if the model is available
   */
  abstract isAvailable(): Promise<boolean>;

  /**
   * Get input schema for this model
   */
  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        text: { type: 'string', description: 'Text prompt for video generation' },
        options: {
          type: 'object',
          properties: {
            duration: { type: 'number', minimum: 1, maximum: 60 },
            width: { type: 'number', minimum: 256, maximum: 2048 },
            height: { type: 'number', minimum: 256, maximum: 2048 },
            aspectRatio: { type: 'string', enum: ['16:9', '9:16', '1:1', '4:3', '3:4'] },
            fps: { type: 'number', minimum: 1, maximum: 60 },
            seed: { type: 'number' }
          }
        }
      },
      required: ['text']
    };
  }

  /**
   * Get output schema for this model
   */
  getOutputSchema(): any {
    return {
      type: 'object',
      description: 'Generated video file',
      properties: {
        format: { type: 'string', enum: ['mp4', 'webm', 'mov'] },
        duration: { type: 'number' },
        width: { type: 'number' },
        height: { type: 'number' },
        fps: { type: 'number' }
      }
    };
  }
}
