/**
 * MediaTransformer Multi-Modal Provider Demo
 * 
 * This demonstrates how the MediaTransformer interface abstracts away the complexity
 * of different multi-modal AI providers into simple, unified transformations.
 */

import { createMediaInput } from '../types/MediaTransformer';
import { transformerRegistry, registerTransformer } from './TransformerRegistry';
import { createFalAiTransformerFromEnv } from './FalAiTransformer';
import { createReplicateTransformerFromEnv } from './ReplicateTransformer';

/**
 * Demo: Multi-Modal AI Provider Integration
 * 
 * Shows how complex provider-specific APIs are abstracted into simple transform() calls
 */
export async function demoMultiModalProviders() {
  console.log('🎯 MediaTransformer Multi-Modal Provider Demo');
  console.log('============================================\n');

  try {
    // Register all available transformers
    console.log('📋 Registering providers...');
    
    // Try to register FAL.ai if API key is available
    try {
      const falAiTransformer = createFalAiTransformerFromEnv();
      registerTransformer(falAiTransformer);
      console.log('✅ FAL.ai provider registered');
    } catch (error) {
      console.log('❌ FAL.ai provider not available (missing FALAI_API_KEY)');
    }

    // Try to register Replicate if API key is available
    try {
      const replicateTransformer = createReplicateTransformerFromEnv();
      registerTransformer(replicateTransformer);
      console.log('✅ Replicate provider registered');
    } catch (error) {
      console.log('❌ Replicate provider not available (missing REPLICATE_API_TOKEN)');
    }

    // Show registry stats
    const stats = transformerRegistry.getStats();
    console.log(`\n📊 Registry Stats:`);
    console.log(`   Total Transformers: ${stats.totalTransformers}`);
    console.log(`   Local Services: ${stats.localTransformers}`);
    console.log(`   Remote Services: ${stats.remoteTransformers}`);
    console.log(`\n🔄 Available Transformations:`);
    
    stats.availableTransformations.forEach(transform => {
      const input = Array.isArray(transform.input) 
        ? transform.input.join(' + ') 
        : transform.input;
      console.log(`   ${input} → ${transform.output} (${transform.transformerCount} providers)`);
    });

    console.log('\n' + '='.repeat(50));
    console.log('🚀 DEMONSTRATION OF UNIFIED INTERFACE POWER');
    console.log('='.repeat(50));

    // Demo 1: Text to Image - could use either FAL.ai FLUX Pro or Replicate FLUX
    console.log('\n1️⃣  TEXT → IMAGE Transformation');
    console.log('   Same interface, different providers under the hood');
    
    const textInput = createMediaInput('text', 'A futuristic city with flying cars at sunset');
    
    try {
      const imageResult = await transformerRegistry.executeTransformation({
        input: textInput,
        outputType: 'image',
        options: { width: 1024, height: 1024 }
      });

      if (imageResult.success) {
        console.log(`   ✅ Generated by: ${imageResult.transformerId}`);
        console.log(`   📄 Output: ${imageResult.output!.type} (${typeof imageResult.output!.data})`);
        console.log(`   ⏱️  Processing time: ${imageResult.processingTime}ms`);
      } else {
        console.log(`   ❌ Failed: ${imageResult.error}`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }

    // Demo 2: Image Animation - requires FAL.ai FramePack (image + text → video)
    console.log('\n2️⃣  IMAGE + TEXT → VIDEO Transformation');
    console.log('   Multi-input transformation example');

    const imageInput = createMediaInput('image', 'data:image/jpeg;base64,/9j/...(base64)'); // Mock data URI
    const animationPrompt = createMediaInput('text', 'Gentle swaying motion with floating particles');
    
    try {
      const videoResult = await transformerRegistry.executeTransformation({
        input: [imageInput, animationPrompt],
        outputType: 'video',
        options: { 
          fps: 30, 
          video_length: 5,
          aspect_ratio: '16:9'
        }
      });

      if (videoResult.success) {
        console.log(`   ✅ Animated by: ${videoResult.transformerId}`);
        console.log(`   📄 Output: ${videoResult.output!.type} (${typeof videoResult.output!.data})`);
        console.log(`   ⏱️  Processing time: ${videoResult.processingTime}ms`);
      } else {
        console.log(`   ❌ Failed: ${videoResult.error}`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }

    // Demo 3: Image Enhancement - requires Replicate Real-ESRGAN (image → enhanced image)
    console.log('\n3️⃣  IMAGE → ENHANCED IMAGE Transformation');
    console.log('   Upscaling and enhancement example');

    const lowResImage = createMediaInput('image', 'https://example.com/low-res-image.jpg');
    
    try {
      const enhancedResult = await transformerRegistry.executeTransformation({
        input: lowResImage,
        outputType: 'image',
        options: { 
          scale: 4, 
          face_enhance: true,
          model: 'real-esrgan' // Force use of Real-ESRGAN
        }
      });

      if (enhancedResult.success) {
        console.log(`   ✅ Enhanced by: ${enhancedResult.transformerId}`);
        console.log(`   📄 Output: ${enhancedResult.output!.type} (${typeof enhancedResult.output!.data})`);
        console.log(`   ⏱️  Processing time: ${enhancedResult.processingTime}ms`);
      } else {
        console.log(`   ❌ Failed: ${enhancedResult.error}`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }

    console.log('\n' + '='.repeat(50));
    console.log('💡 KEY INSIGHTS');
    console.log('='.repeat(50));
    console.log('1. Same transform() interface for all providers');
    console.log('2. Automatic provider selection based on capabilities');  
    console.log('3. Multi-input transformations handled seamlessly');
    console.log('4. Provider-specific options passed through transparently');
    console.log('5. Unified error handling and result format');
    console.log('6. Easy to add new providers without changing client code');

  } catch (error) {
    console.error('Demo failed:', error);
  }
}

/**
 * Show available transformation capabilities
 */
export function showAvailableCapabilities() {
  console.log('\n🔍 DISCOVERING AVAILABLE TRANSFORMATIONS');
  console.log('=========================================');

  const transformations = [
    { input: 'text', output: 'image' },
    { input: 'text', output: 'video' },
    { input: 'image', output: 'image' },
    { input: 'image', output: 'video' }
  ];

  transformations.forEach(({ input, output }) => {
    const providers = transformerRegistry.findTransformers(input as any, output as any);
    console.log(`\n${input} → ${output}:`);
    
    if (providers.length === 0) {
      console.log('   ❌ No providers available');
    } else {
      providers.forEach(provider => {
        const capability = provider.transforms.find(t => 
          (Array.isArray(t.input) ? t.input.includes(input as any) : t.input === input) && 
          t.output === output
        );
        console.log(`   ✅ ${provider.name} (${provider.id})`);
        console.log(`      ${capability?.description}`);
      });
    }
  });
}

// Export for easy testing
export {
  createFalAiTransformerFromEnv,
  createReplicateTransformerFromEnv,
  transformerRegistry,
  registerTransformer
};

// Run demo if this file is executed directly
if (require.main === module) {
  demoMultiModalProviders()
    .then(() => showAvailableCapabilities())
    .catch(console.error);
}
