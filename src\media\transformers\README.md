# Multi-Modal MediaTransformer Architecture

This document demonstrates how the MediaTransformer interface abstracts complex multi-modal AI providers into unified transformation patterns, making it easy to work with different input/output combinations across multiple AI services.

## 🎯 The Power of Abstraction

The MediaTransformer interface shines when dealing with multi-modal AI providers because it hides the complexity of:

1. **Input Interface** - What types of media inputs are required/optional for a transformation
2. **Model Selection** - The specific AI model that supports that input interface  
3. **Endpoint Configuration** - How to actually call that model with its specific parameters/settings
4. **Output Interface** - What type of media is produced
5. **Provider Management** - The service (FAL.ai, Replicate) that hosts multiple models

## 🔧 Supported Providers & Capabilities

### FAL.ai Provider
Extensive reference code exists throughout the codebase. Supports:

1. **FramePack Animation**: `image + text → video`
   ```typescript
   // Input: Static image + animation prompt
   // Output: Animated MP4 video (5-10 seconds)
   falTransformer.transform([imageInput, textInput], 'video', {
     fps: 30, aspect_ratio: '16:9', video_length: 5
   });
   ```

2. **FLUX Pro Generation**: `text → image`
   ```typescript
   // Input: Text prompt + optional negative prompt
   // Output: High-quality images
   falTransformer.transform(textInput, 'image', {
     image_size: 'landscape_4_3', guidance_scale: 3.5
   });
   ```

3. **Runway Gen-3**: `text → video`
   ```typescript
   // Input: Text description
   // Output: Generated video content
   falTransformer.transform(textInput, 'video', {
     duration: 5, aspect_ratio: '16:9'
   });
   ```

4. **Face Swap**: `source_image + target_video → face_swapped_video`
   ```typescript
   // Input: Face image + target video
   // Output: Video with swapped face
   falTransformer.transform([imageInput, videoInput], 'video', {
     face_restore: true, background_enhance: true
   });
   ```

### Replicate Provider
Reference code exists throughout the codebase. Supports:

1. **FLUX 1.1 Pro Ultra**: `text → image`
   ```typescript
   // Input: Text prompt + generation parameters
   // Output: Ultra high-quality images
   replicateTransformer.transform(textInput, 'image', {
     width: 2752, height: 1536, raw: true
   });
   ```

2. **Real-ESRGAN**: `image → enhanced_image`
   ```typescript
   // Input: Low-resolution image
   // Output: Upscaled/enhanced image
   replicateTransformer.transform(imageInput, 'image', {
     scale: 4, face_enhance: true
   });
   ```

3. **Stable Video Diffusion**: `image → video`
   ```typescript
   // Input: Static image
   // Output: Video animation from image
   replicateTransformer.transform(imageInput, 'video', {
     motion_bucket_id: 127, fps: 6, num_frames: 25
   });
   ```

## 🚀 Usage Examples

### Basic Setup
```typescript
import { 
  transformerRegistry, 
  createFalAiTransformerFromEnv,
  createReplicateTransformerFromEnv,
  createMediaInput 
} from './src/media/transformers';

// Register providers (uses environment variables)
transformerRegistry.register(createFalAiTransformerFromEnv());
transformerRegistry.register(createReplicateTransformerFromEnv());
```

### Simple Text → Image
```typescript
const textInput = createMediaInput('text', 'A futuristic city with flying cars');

// Registry automatically selects best available provider
const result = await transformerRegistry.executeTransformation({
  input: textInput,
  outputType: 'image',
  options: { width: 1024, height: 1024 }
});

console.log(`Generated by: ${result.transformerId}`);
console.log(`Output URL: ${result.output.data}`);
```

### Multi-Input Image Animation
```typescript
const imageInput = createMediaInput('image', 'data:image/jpeg;base64,...');
const animationPrompt = createMediaInput('text', 'Gentle swaying with particles');

// Only FAL.ai FramePack supports this transformation
const result = await transformerRegistry.executeTransformation({
  input: [imageInput, animationPrompt],
  outputType: 'video',
  options: { fps: 30, video_length: 5, aspect_ratio: '16:9' }
});
```

### Workflow Chaining
```typescript
// Step 1: Generate image from text (multiple providers available)
const imageResult = await transformerRegistry.executeTransformation({
  input: createMediaInput('text', 'A beautiful landscape'),
  outputType: 'image'
});

// Step 2: Animate the generated image (FAL.ai only)
const videoResult = await transformerRegistry.executeTransformation({
  input: [
    createMediaInput('image', imageResult.output.data),
    createMediaInput('text', 'Gentle motion')
  ],
  outputType: 'video'
});

// Step 3: Alternative - Enhance the image (Replicate only)
const enhancedResult = await transformerRegistry.executeTransformation({
  input: createMediaInput('image', imageResult.output.data),
  outputType: 'image',
  options: { model: 'real-esrgan', scale: 4 }
});
```

## 🔍 Discovery & Provider Selection

### Find Available Transformers
```typescript
// Find all providers that can do text → image
const textToImageProviders = transformerRegistry.findTransformers('text', 'image');
console.log(textToImageProviders.map(p => p.name));
// Output: ['FAL.ai Multi-Modal AI', 'Replicate AI Platform']

// Find providers that can handle multi-input transformations  
const animationProviders = transformerRegistry.findMultiInputTransformers(['image', 'text'], 'video');
console.log(animationProviders.map(p => p.name));
// Output: ['FAL.ai Multi-Modal AI']
```

### Automatic Provider Selection
```typescript
// Registry automatically finds the best available provider
const bestProvider = await transformerRegistry.getBestTransformer('text', 'image', {
  preferLocal: false,  // Prefer remote providers
  excludeTransformers: ['fal-ai']  // Exclude specific providers
});

console.log(`Best provider: ${bestProvider?.name}`);
```

### Registry Statistics
```typescript
const stats = transformerRegistry.getStats();
console.log(`Total providers: ${stats.totalTransformers}`);
console.log(`Available transformations: ${stats.availableTransformations.length}`);

stats.availableTransformations.forEach(transform => {
  const input = Array.isArray(transform.input) 
    ? transform.input.join(' + ') 
    : transform.input;
  console.log(`${input} → ${transform.output} (${transform.transformerCount} providers)`);
});
```

## 🏗️ Architecture Benefits

### 1. **Unified Interface**
```typescript
// Same method signature for all transformations, regardless of complexity
transformer.transform(input, outputType, options);

// Works for simple transformations:
whisperTransformer.transform(audioFile, 'text');

// And complex multi-modal transformations:
falTransformer.transform([image, text], 'video', { fps: 30 });
```

### 2. **Automatic Provider Discovery**
The registry automatically finds compatible providers based on input/output types:
```typescript
// Registry handles provider selection transparently
const result = await transformerRegistry.executeTransformation({
  input: textInput,
  outputType: 'image'
  // No need to specify which provider to use
});
```

### 3. **Provider-Specific Optimizations**
Each transformer can optimize for its provider's strengths:
```typescript
// FAL.ai optimized for real-time animation
falTransformer.transform([image, text], 'video', { 
  teacache: true,    // FAL.ai specific optimization
  guidance_scale: 7.5 
});

// Replicate optimized for ultra-high quality
replicateTransformer.transform(text, 'image', {
  raw: true,         // Replicate FLUX specific
  num_inference_steps: 30
});
```

### 4. **Graceful Fallbacks**
```typescript
// If primary provider fails, automatically try alternatives
const result = await transformerRegistry.executeTransformation({
  input: textInput,
  outputType: 'image'
});

if (!result.success) {
  console.log(`Failed with ${result.transformerId}: ${result.error}`);
  // Registry can automatically retry with different provider
}
```

## 🧪 Testing & Development

Run the comprehensive test suite:
```bash
npm test src/media/transformers/MultiModalProviders.test.ts
```

Try the interactive demo:
```typescript
import { demoMultiModalProviders } from './src/media/transformers/MultiModalDemo';
await demoMultiModalProviders();
```

## 🔮 Future Extensions

The MediaTransformer pattern easily accommodates new providers:

```typescript
// Adding a new provider is simple
class NewProviderTransformer implements MediaTransformer {
  readonly id = 'new-provider';
  readonly transforms = [
    { input: 'audio', output: 'text', description: 'Speech recognition' },
    { input: 'text', output: 'audio', description: 'Text to speech' }
  ];
  
  async transform(input, outputType, options) {
    // Provider-specific implementation
  }
}

// Register and immediately available to all consumers
transformerRegistry.register(new NewProviderTransformer());
```

This architecture makes it trivial to add support for new AI providers, models, and transformation types while maintaining a consistent, simple interface for all consumers.

## 📁 File Structure

```
src/media/transformers/
├── FalAiTransformer.ts           # FAL.ai provider implementation
├── ReplicateTransformer.ts       # Replicate provider implementation  
├── TransformerRegistry.ts        # Provider discovery and management
├── MultiModalDemo.ts             # Interactive demonstration
├── MultiModalProviders.test.ts   # Comprehensive test suite
└── index.ts                      # Export everything
```

The MediaTransformer interface successfully abstracts the complexity of multi-modal AI providers, making sophisticated AI transformations as simple as calling a single `transform()` method.
