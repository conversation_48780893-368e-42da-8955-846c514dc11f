/**
 * Media Types - Compatibility Layer
 *
 * Provides basic types for compatibility with existing code while transitioning to the new Asset system.
 * This file bridges the gap between legacy interfaces and the new role-based Asset system.
 */

// Re-export from new Asset system
export type {
  AudioFormat,
  VideoFormat,
  AudioMetadata,
  VideoMetadata,
  TextMetadata
} from './assets/roles';

// Basic media types
export enum MediaType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  FONT = 'font',
  TEXT = 'text'
}

export enum AspectRatio {
  SQUARE = '1:1',
  LANDSCAPE = '16:9',
  PORTRAIT = '9:16',
  WIDESCREEN = '21:9',
  STANDARD = '4:3'
}

export enum ContentPurpose {
  CONTENT = 'content',
  INTRO = 'intro',
  OUTRO = 'outro',
  OVERLAY = 'overlay',
  BACKGROUND = 'background',
  THUMBNAIL = 'thumbnail'
}

// Basic asset interface for compatibility
export interface BaseAsset {
  id: string;
  path: string;
  filename: string;
  type: MediaType;
  title: string;
  description?: string;
  tags: string[];
  contentPurpose: ContentPurpose[];
  dateCreated: string;
  dateModified: string;
  author?: string;
  license?: string;
  fileSize: number;
  format?: string;
}

// Video asset interface for compatibility
export interface VideoAsset extends BaseAsset {
  type: MediaType.VIDEO;
  format: string;
  width: number;
  height: number;
  duration: number;
  frameRate?: number;
  bitrate?: number;
  thumbnailPath?: string;
  hasAudio?: boolean;
  hasCaptions?: boolean;
  transcript?: string;
  transcriptLanguage?: string;
  transcriptConfidence?: number;
  transcriptGeneratedAt?: string;
}

// Audio asset interface for compatibility
export interface AudioAsset extends BaseAsset {
  type: MediaType.AUDIO;
  format: string;
  duration: number;
  sampleRate?: number;
  channels?: number;
  bitrate?: number;
  transcript?: string;
  transcriptLanguage?: string;
  transcriptConfidence?: number;
  transcriptGeneratedAt?: string;
}

// Image asset interface for compatibility
export interface ImageAsset extends BaseAsset {
  type: MediaType.IMAGE;
  format: string;
  width: number;
  height: number;
  colorSpace?: string;
  dpi?: number;
}

// Font asset interface for compatibility
export interface FontAsset extends BaseAsset {
  type: MediaType.FONT;
  format: string;
  fontFamily: string;
  fontWeight?: string;
  fontStyle?: string;
}

// Filter options for compatibility
export interface BaseFilterOptions {
  tags?: string[];
  contentPurpose?: ContentPurpose[];
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface VideoFilterOptions extends BaseFilterOptions {
  format?: string;
  minDuration?: number;
  maxDuration?: number;
  hasAudio?: boolean;
  hasCaptions?: boolean;
}

export interface AudioFilterOptions extends BaseFilterOptions {
  format?: string;
  minDuration?: number;
  maxDuration?: number;
  hasTranscript?: boolean;
}

export interface ImageFilterOptions extends BaseFilterOptions {
  format?: string;
  minWidth?: number;
  maxWidth?: number;
  minHeight?: number;
  maxHeight?: number;
}

export interface FontFilterOptions extends BaseFilterOptions {
  fontFamily?: string;
  fontWeight?: string;
  fontStyle?: string;
}

// Database interface for compatibility
export interface MediaDatabase {
  images: ImageAsset[];
  videos: VideoAsset[];
  audio: AudioAsset[];
  fonts: FontAsset[];
}

// Type guards for compatibility
export function isVideoAsset(asset: BaseAsset): asset is VideoAsset {
  return asset.type === MediaType.VIDEO;
}

export function isAudioAsset(asset: BaseAsset): asset is AudioAsset {
  return asset.type === MediaType.AUDIO;
}

export function isImageAsset(asset: BaseAsset): asset is ImageAsset {
  return asset.type === MediaType.IMAGE;
}

export function isFontAsset(asset: BaseAsset): asset is FontAsset {
  return asset.type === MediaType.FONT;
}