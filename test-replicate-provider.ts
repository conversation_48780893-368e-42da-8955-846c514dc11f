/**
 * Test ReplicateProvider Implementation
 * 
 * Tests the new ReplicateProvider with provider role system
 */

import { ReplicateProvider } from './packages/providers/src/remote/ReplicateProvider';
import { 
  hasTextToImageRole, 
  hasTextToVideoRole, 
  hasVideoToVideoRole,
  getProviderRoles 
} from './src/media/registry/ProviderRoles';
import { MediaCapability } from './packages/core/src/types/provider';

async function testReplicateProvider() {
  console.log('🧪 Testing ReplicateProvider with Provider Role System');
  
  // Test provider creation
  const provider = new ReplicateProvider();
  
  console.log('\n📝 Provider Information:');
  console.log('ID:', provider.id);
  console.log('Name:', provider.name);
  console.log('Type:', provider.type);
  console.log('Capabilities:', provider.capabilities);
  
  // Test provider roles
  console.log('\n🎭 Provider Roles:');
  console.log('Has TextToImage role:', hasTextToImageRole(provider));
  console.log('Has TextToVideo role:', hasTextToVideoRole(provider));
  console.log('Has VideoToVideo role:', hasVideoToVideoRole(provider));
  console.log('All roles:', getProviderRoles(provider));
  
  // Test model filtering by role
  if (hasTextToImageRole(provider)) {
    console.log('\n🖼️ Text-to-Image Models:');
    const textToImageModels = provider.getSupportedTextToImageModels();
    textToImageModels.forEach(modelId => {
      const model = provider.models.find(m => m.id === modelId);
      console.log(`  - ${modelId}: ${model?.name}`);
    });
  }
  
  if (hasTextToVideoRole(provider)) {
    console.log('\n🎬 Text-to-Video Models:');
    const textToVideoModels = provider.getSupportedTextToVideoModels();
    textToVideoModels.forEach(modelId => {
      const model = provider.models.find(m => m.id === modelId);
      console.log(`  - ${modelId}: ${model?.name}`);
    });
  }
  
  if (hasVideoToVideoRole(provider)) {
    console.log('\n🎞️ Video-to-Video Models:');
    const videoToVideoModels = provider.getSupportedVideoToVideoModels();
    videoToVideoModels.forEach(modelId => {
      const model = provider.models.find(m => m.id === modelId);
      console.log(`  - ${modelId}: ${model?.name}`);
    });
  }
  
  // Test capability-based model filtering
  console.log('\n🔍 Models by Capability:');
  for (const capability of provider.capabilities) {
    const models = provider.getModelsForCapability(capability);
    console.log(`  ${capability}: ${models.length} models`);
    models.forEach(model => {
      console.log(`    - ${model.id}: ${model.name}`);
    });
  }
  
  // Test configuration (without actual API key)
  console.log('\n⚙️ Testing Configuration:');
  try {
    await provider.configure({
      apiKey: 'test-key' // This will fail validation, but tests the method
    });
  } catch (error) {
    console.log('Configuration test (expected failure):', error.message);
  }
  
  // Test availability check (without configuration)
  console.log('\n🔌 Testing Availability:');
  const isAvailable = await provider.isAvailable();
  console.log('Provider available:', isAvailable);
  
  console.log('\n✅ ReplicateProvider test completed!');
}

// Only run if this file is executed directly
if (require.main === module) {
  testReplicateProvider().catch(console.error);
}

export { testReplicateProvider };
